// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.h
// 描述: 系统模式管理头文件，定义不同工作模式和状态机

#ifndef __APP_MODE_H
#define __APP_MODE_H

#include "main.h"
#include "MultiTimer.h"
#include <stdbool.h>

// 前向声明 - 使用与app_maixcam.h相同的防护宏
#ifndef LASERCOORD_T_DEFINED
#define LASERCOORD_T_DEFINED
typedef struct {
    char type;    // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;        // X坐标
    int y;        // Y坐标
} LaserCoord_t;
#endif

// 系统工作模式枚举
typedef enum {
    MODE_IDLE = 0,      // 默认状态：只使能，不追踪
    MODE_TRACKING,      // 模式一：追踪模式
    MODE_CUSTOM,        // 模式二：自定义模式（预留）
    MODE_MAX            // 模式数量（用于循环切换）
} system_mode_t;

// 函数声明
void app_mode_init(void);                    // 模式管理初始化
void app_mode_switch(void);                  // 模式切换
system_mode_t app_mode_get_current(void);    // 获取当前模式
const char* app_mode_get_name(system_mode_t mode); // 获取模式名称

// 模式二相关函数
bool mode2_check_new_data(void);  // 检查是否收到新数据
void mode2_monitor_task(MultiTimer *timer, void *userData);  // 模式二监控任务

#endif /* __APP_MODE_H */
