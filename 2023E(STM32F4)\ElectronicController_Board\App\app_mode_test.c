// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode_test.c
// 描述: 模式管理功能测试文件

#include "app_mode.h"
#include "app_uasrt.h"

/**
 * @brief 测试空闲模式随机旋转功能
 */
void test_idle_random_rotation(void)
{
    my_printf(&huart1, "=== 测试空闲模式随机旋转 ===\r\n");
    
    // 启动随机旋转
    idle_random_rotation_start();
    my_printf(&huart1, "随机旋转已启动\r\n");
    
    // 等待5秒观察旋转效果
    HAL_Delay(5000);
    
    // 停止随机旋转
    idle_random_rotation_stop();
    my_printf(&huart1, "随机旋转已停止\r\n");
}

/**
 * @brief 测试模式切换功能
 */
void test_mode_switching(void)
{
    my_printf(&huart1, "=== 测试模式切换功能 ===\r\n");
    
    // 显示当前模式
    my_printf(&huart1, "当前模式: %s\r\n", app_mode_get_name(app_mode_get_current()));
    
    // 模拟按键切换
    my_printf(&huart1, "模拟第1次按键...\r\n");
    app_mode_switch();
    HAL_Delay(2000);
    
    my_printf(&huart1, "模拟第2次按键...\r\n");
    app_mode_switch();
    HAL_Delay(2000);
    
    my_printf(&huart1, "模拟第3次按键...\r\n");
    app_mode_switch();
    HAL_Delay(2000);
    
    my_printf(&huart1, "模拟第4次按键...\r\n");
    app_mode_switch();
    HAL_Delay(2000);
    
    my_printf(&huart1, "测试完成\r\n");
}

/**
 * @brief 运行所有测试
 */
void run_mode_tests(void)
{
    my_printf(&huart1, "开始模式管理功能测试...\r\n");
    
    test_idle_random_rotation();
    HAL_Delay(1000);
    
    test_mode_switching();
    
    my_printf(&huart1, "所有测试完成!\r\n");
}
