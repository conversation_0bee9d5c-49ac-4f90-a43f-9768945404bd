/**
 * @file app_motor.h
 * @brief ����EmmV5�ĵ�����ƺ���
 * @copyright �״׵��ӹ�����
 */

#ifndef __APP_MOTOR_H_
#define __APP_MOTOR_H_

#include "mydefine.h"

/* ������ƺ궨�� */
#define MOTOR_X_ADDR        0x01          // X������ַ
#define MOTOR_Y_ADDR        0x01          // Y������ַ
#define MOTOR_X_UART        huart2        // X�������� ��
#define MOTOR_Y_UART        huart5        // Y�������� ��
#define MOTOR_MAX_SPEED     30            // ������ת��(RPM)
#define MOTOR_ACCEL         0             // ������ٶ�(0��ʾֱ������)
#define MOTOR_SYNC_FLAG     false         // ���ͬ����־
#define MOTOR_MAX_ANGLE     80              // ������Ƕ�����(��50��)

/* �������� */
void Motor_Init(void);                    // �����ʼ��
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);  // ����XY����ٶ�(�ٷֱ�)
void Motor_Stop(void);                    // ֹͣ���е��

#endif /* __APP_MOTOR_H_ */
