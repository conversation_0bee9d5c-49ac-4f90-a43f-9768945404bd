// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.c
// 描述: 系统模式管理实现文件，处理不同工作模式的切换和管理

#include "app_mode.h"
#include "app_pid.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_motor.h"

// 当前系统模式
static system_mode_t current_mode = MODE_IDLE;

// 模式二相关变量
static uint8_t mode2_no_data_flag = 0;  // 无数据标志位：1=无新数据，0=有新数据
static uint32_t mode2_last_data_time = 0;  // 上次接收数据的时间戳
static uint8_t mode2_x_speed = 80;  // X轴转动速度百分比

// 空闲模式随机旋转相关变量
static uint8_t idle_rotation_active = 0;  // 随机旋转激活标志：1=激活，0=停止
static uint32_t idle_rotation_change_time = 0;  // 下次改变方向的时间
static int8_t idle_rotation_direction = 1;  // 当前旋转方向：1=正向，-1=反向
static uint8_t idle_rotation_speed = 25;  // 随机旋转速度百分比

// 模式名称数组
static const char* mode_names[] = {
    "IDLE",      // 空闲模式
    "TRACKING",  // 追踪模式
    "CUSTOM"     // 自定义模式（预留）
};

/**
 * @brief 模式管理初始化
 */
void app_mode_init(void)
{
    current_mode = MODE_IDLE; // 上电默认为空闲模式

    // 确保MaixCam回调函数为默认回调（支持模式检查）
    maixcam_set_callback(NULL); // NULL会恢复为默认回调函数

    // 确保PID处于停止状态
    app_pid_stop();

    // 初始化随机旋转变量
    idle_rotation_active = 0;
    idle_rotation_change_time = 0;
    idle_rotation_direction = 1;
    idle_rotation_speed = 25;

    // 启动空闲模式随机旋转
    idle_random_rotation_start();

    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 模式切换函数
 */
void app_mode_switch(void)
{
    // 退出当前模式
    switch(current_mode)
    {
        case MODE_IDLE:
            // 退出空闲模式：停止随机旋转
            idle_random_rotation_stop();
            my_printf(&huart1, "Exit Idle Mode\r\n");
            break;
            
        case MODE_TRACKING:
            // 退出追踪模式：停止PID控制
            app_pid_stop();
            my_printf(&huart1, "Exit Tracking Mode\r\n");
            break;
            
        case MODE_CUSTOM:
            // 模式二退出处理：停止电机和定时器
            Motor_Stop();
            mode2_no_data_flag = 0;
            my_printf(&huart1, "Exit Custom Mode\r\n");
            break;
            
        default:
            break;
    }
    
    // 切换到下一个模式（按照：空闲→追踪→空闲→自定义→空闲的循环）
    static uint8_t switch_count = 0; // 按键计数器

    switch_count++;

    if(switch_count == 1)
    {
        current_mode = MODE_TRACKING; // 第1次按键：进入追踪模式
    }
    else if(switch_count == 2)
    {
        current_mode = MODE_IDLE;     // 第2次按键：回到空闲模式
    }
    else if(switch_count == 3)
    {
        current_mode = MODE_CUSTOM;   // 第3次按键：进入自定义模式
    }
    else
    {
        current_mode = MODE_IDLE;     // 第4次按键：回到空闲模式
        switch_count = 0;             // 重置计数器，下次从追踪模式开始
    }
    
    // 进入新模式
    switch(current_mode)
    {
        case MODE_IDLE:
            // 进入空闲模式：启动随机旋转
            idle_random_rotation_start();
            my_printf(&huart1, "Enter Idle Mode - Random X-axis rotation\r\n");
            break;
            
        case MODE_TRACKING:
            // 进入追踪模式：启动PID控制
            my_printf(&huart1, "Enter Tracking Mode\r\n");
            app_pid_init();
            app_pid_start();
            break;
            
        case MODE_CUSTOM:
            // 模式二进入处理：初始化相关变量和定时器
            mode2_no_data_flag = 1;  // 初始设为无数据状态
            mode2_last_data_time = HAL_GetTick();

            // 启动X轴智能搜索（比随机旋转更有目的性）
            Motor_Set_Speed(mode2_x_speed, 0);

            // 启动模式二监控定时器
            multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);  // 100ms检查一次
            my_printf(&huart1, "Enter Custom Mode - Intelligent X-axis searching\r\n");
            break;
            
        default:
            break;
    }
    
    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 获取当前模式
 * @return 当前系统模式
 */
system_mode_t app_mode_get_current(void)
{
    return current_mode;
}

/**
 * @brief 获取模式名称
 * @param mode 模式枚举值
 * @return 模式名称字符串
 */
const char* app_mode_get_name(system_mode_t mode)
{
    if(mode < MODE_MAX)
        return mode_names[mode];
    else
        return "UNKNOWN";
}

/**
 * @brief 检查是否收到新的to:(x,y)数据
 * @return true=收到新数据，false=无新数据
 */
bool mode2_check_new_data(void)
{
    extern int target_x_coord, target_y_coord;  // 来自app_maixcam.c的全局变量
    static int last_x = -1, last_y = -1;  // 记录上次的坐标值

    // 检查坐标是否发生变化
    if (target_x_coord != last_x || target_y_coord != last_y) {
        // 更新记录的坐标
        last_x = target_x_coord;
        last_y = target_y_coord;

        // 更新时间戳和标志位
        mode2_last_data_time = HAL_GetTick();
        mode2_no_data_flag = 0;

        return true;  // 收到新数据
    }

    return false;  // 无新数据
}

/**
 * @brief 模式二监控任务
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void mode2_monitor_task(MultiTimer *timer, void *userData)
{
    if (current_mode != MODE_CUSTOM) {
        return;  // 不在模式二时退出
    }

    // 检查是否收到新的to:(x,y)数据
    if (mode2_check_new_data()) {
        // 收到新数据，切换到模式一
        my_printf(&huart1, "Mode2: New data detected - Switch to Tracking Mode\r\n");
        current_mode = MODE_TRACKING;

        // 停止X轴旋转
        Motor_Stop();

        // 启动追踪模式
        app_pid_init();
        app_pid_start();

        my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
        return;  // 切换模式后退出，不再重启定时器
    }

    // 重新启动定时器
    multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);
}

/**
 * @brief 生成伪随机数
 * @param min 最小值
 * @param max 最大值
 * @return 随机数
 */
static uint32_t generate_pseudo_random(uint32_t min, uint32_t max)
{
    static uint32_t seed = 0;
    if (seed == 0) {
        seed = HAL_GetTick();  // 使用系统时钟作为初始种子
    }

    // 简单的线性同余生成器
    seed = (seed * 1103515245 + 12345) & 0x7fffffff;
    return min + (seed % (max - min + 1));
}

/**
 * @brief 启动空闲模式随机旋转
 */
void idle_random_rotation_start(void)
{
    if (idle_rotation_active) {
        return;  // 已经在运行
    }

    idle_rotation_active = 1;
    idle_rotation_change_time = HAL_GetTick() + generate_pseudo_random(1000, 3000);  // 1-3秒后改变方向
    idle_rotation_direction = (generate_pseudo_random(0, 1) == 0) ? -1 : 1;  // 随机初始方向

    // 启动X轴随机旋转
    Motor_Set_Speed(idle_rotation_direction * idle_rotation_speed, 0);

    // 启动随机旋转监控定时器
    multiTimerStart(&mt_user, 200, idle_random_rotation_task, NULL);  // 200ms检查一次
}

/**
 * @brief 停止空闲模式随机旋转
 */
void idle_random_rotation_stop(void)
{
    if (!idle_rotation_active) {
        return;  // 已经停止
    }

    idle_rotation_active = 0;
    Motor_Stop();  // 停止电机
}

/**
 * @brief 空闲模式随机旋转任务
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void idle_random_rotation_task(MultiTimer *timer, void *userData)
{
    if (!idle_rotation_active || current_mode != MODE_IDLE) {
        return;  // 不在空闲模式或已停止
    }

    uint32_t current_time = HAL_GetTick();

    // 检查是否需要改变方向
    if (current_time >= idle_rotation_change_time) {
        // 随机改变方向
        idle_rotation_direction = (generate_pseudo_random(0, 1) == 0) ? -1 : 1;

        // 随机改变速度（20-40%）
        idle_rotation_speed = generate_pseudo_random(20, 40);

        // 设置下次改变时间（1-4秒）
        idle_rotation_change_time = current_time + generate_pseudo_random(1000, 4000);

        // 应用新的旋转参数
        Motor_Set_Speed(idle_rotation_direction * idle_rotation_speed, 0);

        my_printf(&huart1, "Idle rotation: dir=%d, speed=%d%%\r\n",
                  idle_rotation_direction, idle_rotation_speed);
    }

    // 重新启动定时器
    multiTimerStart(&mt_user, 200, idle_random_rotation_task, NULL);
}
